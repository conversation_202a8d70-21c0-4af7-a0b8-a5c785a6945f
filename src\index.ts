import { initFireBase, db, storage } from "./firestart";
import { v4 as uuidv4 } from "uuid";
import properties from "./properties";
import propertyTypes from "./propertyTypes";

initFireBase();

// **************************************** //

const citiesUsed = function () {
  const cities = new Set();
  properties.forEach((property) => {
    cities.add(property.city);
  });
  return Array.from(cities);
};

async function insertPropertiesToFirestore() {
  try {
    const propertiesCollection = db.collection("properties");

    // Insert each property as a separate document with generated UUID
    const insertPromises = properties.map(async (property) => {
      // Generate UUID for the document ID
      const generatedId = uuidv4();

      // Create a copy of the property with the generated ID
      const propertyWithId = {
        ...property,
        id: generatedId,
      };

      // Insert with the generated UUID as document ID
      return propertiesCollection.doc(generatedId).set(propertyWithId);
      
    });

    const results = await Promise.all(insertPromises);
    console.log(
      `Successfully inserted ${results.length} properties into Firestore`
    );

    // Log the generated document IDs
    properties.forEach((property, index) => {
      console.log(
        `Property ${property.title} inserted with ID: ${
          property.id || "UUID generated"
        }`
      );
    });
  } catch (error) {
    console.error("Error inserting properties to Firestore:", error);
    throw error;
  }
}

// **************************************** //
async function insertProprtyTTypesToFirebase() {
  try {
    const propertyTypesCollection = db.collection("propertyTypes");

    // Insert each property type as a separate document with generated UUID
    const insertPromises = propertyTypes.map(async (propertyType: any) => {
      // Generate UUID for the document ID
      const generatedId = uuidv4();

      // Create a copy of the property type with the generated ID
      const propertyTypeWithId = {
        ...propertyType,
        id: generatedId,
      };

      // Insert with the generated UUID as document ID
      return propertyTypesCollection.doc(generatedId).set(propertyTypeWithId);
    });
  } catch (error) {
    console.error("Error inserting property types to Firestore:", error);
    throw error;
  }
}

async function main() {
  // Insert properties into Firestore and log the cities used
  // console.log(citiesUsed());
  // await insertProprtyTTypesToFirebase();
  await insertPropertiesToFirestore();
}

main().catch((error) => console.error("Error:", error));
